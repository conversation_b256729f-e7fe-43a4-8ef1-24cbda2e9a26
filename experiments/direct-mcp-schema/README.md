# 直接MCP客户端调用验证实验

## 目标

验证不使用LLM Agent，直接调用MCP客户端获取工具schema的方法，解决步数限制问题。

## 问题分析

当前 `get_tool_schema()` 函数存在的问题：

1. **架构过度设计**：使用MCPAgent通过LLM来执行确定性的API调用
2. **步数限制**：即使增加到100步仍然超限
3. **效率低下**：LLM需要多步推理来理解如何调用MCP工具
4. **不必要的复杂性**：schema获取是确定性操作，不需要LLM推理

## 解决方案

直接使用MCP客户端的原生方法：

```python
# 当前方法（有问题）
result = await agent.run(
    f"获取工具 {tool_name} 的完整 schema 信息...",
    max_steps=100
)

# 新方法（直接调用）
tools = await client.list_tools()
target_tool = next(tool for tool in tools if tool.name == tool_name)
schema_info = {
    "name": target_tool.name,
    "description": target_tool.description,
    "parameters": target_tool.inputSchema
}
```

## 预期优势

1. **消除步数限制**：不使用LLM，无步数概念
2. **提高效率**：直接API调用，毫秒级响应
3. **确保准确性**：直接获取原始数据，无LLM解释偏差
4. **简化代码**：纯函数式，符合项目偏好

## 测试文件

- `direct_schema_test.py`: 验证脚本
- `README.md`: 说明文档

## 运行方法

```bash
cd experiments/direct-mcp-schema
uv run python direct_schema_test.py
```

## 验证内容

1. 基本直接调用测试
2. 获取指定工具schema测试  
3. 效率对比测试

## 成功标准

- 能够成功获取工具schema
- 无步数限制问题
- 响应时间显著优于Agent方法
- 返回数据格式正确且完整
