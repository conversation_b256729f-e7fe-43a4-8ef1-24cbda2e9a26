#!/usr/bin/env python3
"""
优化的MCP工具schema获取验证脚本
验证使用最小化Agent调用获取工具schema的方法
"""

import os
import json
import asyncio
import time
from pathlib import Path
from dotenv import load_dotenv
from mcp_use import MCPClient, MCPAgent

# 添加src路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src'))
from llm_config import create_llm


async def test_minimal_agent_schema_call():
    """测试最小化Agent调用获取schema"""
    print("🚀 begin test_minimal_agent_schema_call")

    # 加载环境变量
    load_dotenv()

    server_name = os.getenv("MCP_SERVER_NAME")
    server_url = os.getenv("MCP_SERVER_URL")

    print(f"📡 连接MCP服务器: {server_name} @ {server_url}")

    # 创建MCP客户端配置
    mcp_config = {
        "mcpServers": {
            server_name: {
                "url": server_url
            }
        }
    }

    # 创建MCP客户端和Agent
    client = MCPClient.from_dict(mcp_config)
    llm = create_llm()
    agent = MCPAgent(llm=llm, client=client, max_steps=20)  # 增加步数
    print("✅ MCP Agent创建成功")

    try:
        # 使用最简单的指令获取工具列表
        print("\n📋 获取工具列表...")
        start_time = time.time()

        result = await agent.run(
            "列出所有可用工具的名称",
            max_steps=20
        )

        list_time = time.time() - start_time
        print(f"⚡ 工具列表获取耗时: {list_time:.2f}秒")
        print(f"📋 工具列表结果:")
        print(result)

        return result

    except Exception as e:
        print(f"❌ 最小化Agent调用失败: {e}")
        raise

    finally:
        print("🔚 end test_minimal_agent_schema_call")


async def test_optimized_schema_call(tool_name: str):
    """测试优化的schema获取方法"""
    print(f"🚀 begin test_optimized_schema_call(tool_name={tool_name})")

    load_dotenv()

    server_name = os.getenv("MCP_SERVER_NAME")
    server_url = os.getenv("MCP_SERVER_URL")

    mcp_config = {
        "mcpServers": {
            server_name: {
                "url": server_url
            }
        }
    }

    client = MCPClient.from_dict(mcp_config)
    llm = create_llm()
    agent = MCPAgent(llm=llm, client=client, max_steps=20)  # 增加步数

    try:
        # 使用最简单的指令获取特定工具schema
        print(f"🔍 获取工具 {tool_name} 的schema...")
        start_time = time.time()

        result = await agent.run(
            f"获取工具 {tool_name} 的schema信息",
            max_steps=2
        )

        schema_time = time.time() - start_time
        print(f"⚡ Schema获取耗时: {schema_time:.2f}秒")
        print(f"📋 Schema结果:")
        print(result)

        return result

    except Exception as e:
        print(f"❌ 优化schema调用失败: {e}")
        return f"Error: {e}"

    finally:
        print(f"🔚 end test_optimized_schema_call(tool_name={tool_name})")


async def compare_methods():
    """对比不同方法的效率"""
    print("🚀 begin compare_methods")

    # 测试1: 最小化Agent调用
    print("\n📋 测试1: 最小化Agent调用获取工具列表")
    await test_minimal_agent_schema_call()

    # 测试2: 优化的schema获取
    print("\n📋 测试2: 优化的schema获取")
    await test_optimized_schema_call("LIST_CLUSTERS")

    print("🔚 end compare_methods")


async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 优化MCP工具schema获取验证实验")
    print("=" * 60)

    # 运行对比测试
    await compare_methods()

    print("\n" + "=" * 60)
    print("✅ 验证实验完成")
    print("\n� 结论:")
    print("- 使用最小步数(2-3步)的Agent调用")
    print("- 简化指令，避免复杂要求")
    print("- 专注于单一任务，避免多步骤操作")


if __name__ == "__main__":
    asyncio.run(main())
