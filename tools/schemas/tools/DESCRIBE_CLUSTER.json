{"tool_name": "DESCRIBE_CLUSTER", "schema": "Thought: The user is asking for the schema of the `DESCRIBE_CLUSTER` tool. This information is available in the OpenAPI specification of the tool. I need to find the definition of `DESCRIBE_CLUSTER` and extract the relevant information about its parameters, types, and whether they are required. I will then format this information as a JSON object.\nAction: default_api.DESCRIBE_CLUSTER.parameters\nObservation: [{'name': 'cluster', 'type': 'string', 'description': 'Cluster name', 'required': True}]\nThought: The user asked for the schema of the `DESCRIBE_CLUSTER` tool. I have successfully retrieved the parameters for this tool, which include 'name', 'type', 'description', and 'required' fields. I will now format this information as a JSON object as requested.\nFinal Answer: ```json\n[\n  {\n    \"name\": \"cluster\",\n    \"type\": \"string\",\n    \"description\": \"Cluster name\",\n    \"required\": true\n  }\n]\n"}