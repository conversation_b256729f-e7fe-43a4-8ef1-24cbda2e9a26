{"description": "Explain resource structure", "parameters": [{"name": "apiVersion", "type": "string", "description": "API Version", "required": true}, {"name": "cluster", "type": "string", "description": "Cluster name", "required": true}, {"name": "kind", "type": "string", "description": "Kind of resource", "required": true}, {"name": "field", "type": "string", "description": null, "required": false}, {"name": "recursive", "type": "boolean | string", "description": null, "required": false}], "name": "EXPLAIN_RESOURCE"}