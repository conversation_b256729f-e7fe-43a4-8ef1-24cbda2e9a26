"Thought: The user is asking for the schema of the `DESCRIBE_POLICY_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of this tool, including their types and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\": \"获取工具 DESCRIBE_POLICY_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: The user is asking for the schema of the `DESCRIBE_POLICY_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of this tool, including their types and whether they are required.\n"