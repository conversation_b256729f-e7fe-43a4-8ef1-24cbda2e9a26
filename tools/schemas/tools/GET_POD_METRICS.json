{"tool_name": "GET_POD_METRICS", "schema": "Thought: The user is asking for the schema information of the `GET_POD_METRICS` tool. This information is available in the OpenAPI specification of the tool. I need to present this information in a structured JSON format, including input parameters, their types, and whether they are required.\nAction: default_api.GET_POD_METRICS.__doc__\nObservation: Get Kubernetes pod metrics\n\n  Args:\n    cluster: Cluster name\n    fieldSelector: \n    labelSelector: \n    limit: \n    namespace: \n    podName: \n    sortBy: \nThought: The user wants the schema of the `GET_POD_METRICS` function. I have the docstring for the function, which contains the parameter information. I will parse this information and present it in the requested JSON format.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_POD_METRICS\",\n  \"description\": \"Get Kubernetes pod metrics\",\n  \"parameters\": [\n    {\n      \"name\": \"cluster\",\n      \"type\": \"str\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"fieldSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"limit\",\n      \"type\": \"float | str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"podName\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"sortBy\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n"}