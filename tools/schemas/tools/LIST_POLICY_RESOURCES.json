{"tool_name": "LIST_POLICY_RESOURCES", "schema": "Thought: The user is asking for the schema of the `LIST_POLICY_RESOURCES` tool. I need to provide the input parameters, their types, and whether they are required. I will format this information as a JSON object.\nAction: default_api.LIST_POLICY_RESOURCES\nAction Input: {}\nObservation: An error occurred: LIST_POLICY_RESOURCES missing 3 required parameters: apiVersion, cluster, kind\nThought: The user is asking for the schema of the `LIST_POLICY_RESOURCES` tool, not to execute it. The error message indicates that I tried to execute the tool without the required parameters. I need to provide the schema information, which is available from the tool's definition. I will describe the parameters of the `LIST_POLICY_RESOURCES` function.\nFinal Answer: ```json\n{\n  \"tool_name\": \"LIST_POLICY_RESOURCES\",\n  \"description\": \"List policy Kubernetes resources (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"API Version\"\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Kind of resource\"\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n"}