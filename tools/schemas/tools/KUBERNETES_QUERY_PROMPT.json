{"tool_name": "KUBERNETES_QUERY_PROMPT", "schema": "Thought: The user is asking for the schema of the `KUBERNETES_QUERY_PROMPT` tool. I need to provide the input parameters, their types, and whether they are required, in JSON format. I can get this information from the tool definitions.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input:\nObservation: The tool KUBERNETES_QUERY_PROMPT is not a valid tool. I cannot directly get the schema of a tool using a tool. I need to provide the schema information based on the provided tool definitions.\nThought: The user is asking for the schema of the `KUBERNETES_QUERY_PROMPT` tool. I need to provide the input parameters, their types, and whether they are required, in JSON format. I can get this information from the tool definitions provided to me.\n\nThe `KUBERNETES_QUERY_PROMPT` tool has the following definition:\n```python\ndef KUBERNETES_QUERY_PROMPT(\n    task: str,\n    context: str | None = None,\n) -> :\n  \"\"\"Kubernetes操作指导\n\n  Args:\n    task: 需要执行的任务描述\n    context:\n  \"\"\"\n```\nFrom this definition, I can extract the following schema information:\n- `task`: type `str`, required.\n- `context`: type `str`, optional.\n\nNow I will format this information as a JSON object.\nFinal Answer: ```json\n{\n  \"tool_name\": \"KUBERNETES_QUERY_PROMPT\",\n  \"description\": \"Kubernetes操作指导\",\n  \"parameters\": [\n    {\n      \"name\": \"task\",\n      \"type\": \"str\",\n      \"description\": \"需要执行的任务描述\",\n      \"required\": true\n    },\n    {\n      \"name\": \"context\",\n      \"type\": \"str\",\n      \"description\": \"\",\n      \"required\": false\n    }\n  ]\n}\n"}