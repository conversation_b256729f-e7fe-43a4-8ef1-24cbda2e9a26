{"name": "ANALYZE_POD_LOGS", "description": "Analyze logs from a Pod and provide insights", "parameters": {"type": "object", "properties": {"cluster": {"type": "string", "description": "Cluster name"}, "name": {"type": "string", "description": "Name of the Pod"}, "container": {"type": "string"}, "errorPattern": {"type": "string"}, "namespace": {"type": "string"}, "previous": {"type": "string", "format": "boolean"}, "prompt": {"type": "string"}, "tailLines": {"type": "string", "format": "number"}}, "required": ["cluster", "name"]}}