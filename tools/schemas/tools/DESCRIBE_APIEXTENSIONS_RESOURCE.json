{"tool_name": "DESCRIBE_APIEXTENSIONS_RESOURCE", "schema": "Thought: The user is asking for the schema of the `DESCRIBE_APIEXTENSIONS_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to find the definition of this tool and extract the relevant information, including input parameters, their types, and whether they are required. I will then format this information as a JSON object.\nAction: default_api.DESCRIBE_APIEXTENSIONS_RESOURCE.api_schema()\nObservation: {'name': 'DESCRIBE_APIEXTENSIONS_RESOURCE', 'description': 'Describe a specific apiextensions.k8s.io resource with detailed information (cluster-scoped)', 'parameters': {'apiVersion': {'type': 'string'}, 'cluster': {'type': 'string'}, 'kind': {'type': 'string'}, 'name': {'type': 'string'}, 'namespace': {'type': 'string'}}, 'required': ['apiVersion', 'cluster', 'kind', 'name']}\nFinal Answer: ```json\n{\n  \"name\": \"DESCRIBE_APIEXTENSIONS_RESOURCE\",\n  \"description\": \"Describe a specific apiextensions.k8s.io resource with detailed information (cluster-scoped)\",\n  \"parameters\": {\n    \"apiVersion\": {\n      \"type\": \"string\"\n    },\n    \"cluster\": {\n      \"type\": \"string\"\n    },\n    \"kind\": {\n      \"type\": \"string\"\n    },\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"namespace\": {\n      \"type\": \"string\"\n    }\n  },\n  \"required\": [\n    \"apiVersion\",\n    \"cluster\",\n    \"kind\",\n    \"name\"\n  ]\n}\n"}