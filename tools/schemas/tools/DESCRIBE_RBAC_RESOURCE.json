{"tool_name": "DESCRIBE_RBAC_RESOURCE", "schema": "Thought: The user is asking for the schema of the `DESCRIBE_RBAC_RESOURCE` tool. I need to provide the input parameters, their types, and whether they are required, in JSON format. I will use the `default_api.DESCRIBE_RBAC_RESOURCE` function's signature to extract this information.\nAction: default_api.DESCRIBE_RBAC_RESOURCE\nAction Input: {}\nObservation: An error occurred: Missing required parameter 'apiVersion'\nMissing required parameter 'cluster'\nMissing required parameter 'kind'\nMissing required parameter 'name'\nThought: The previous attempt to get the schema by calling the tool without arguments resulted in a \"Missing required parameter\" error, which is expected as I'm trying to inspect the schema itself, not execute the tool. I need to provide the schema information based on the tool definition.\n\nThe `DESCRIBE_RBAC_RESOURCE` tool has the following parameters:\n- `apiVersion`: string, required\n- `cluster`: string, required\n- `kind`: string, required\n- `name`: string, required\n- `namespace`: string, optional\n\nI will format this information as a JSON object.\nFinal Answer: ```json\n{\n  \"tool_name\": \"DESCRIBE_RBAC_RESOURCE\",\n  \"description\": \"Describe a specific rbac.authorization.k8s.io resource with detailed information (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"API Version\"\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Kind of resource\"\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Name of the resource\"\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n```"}