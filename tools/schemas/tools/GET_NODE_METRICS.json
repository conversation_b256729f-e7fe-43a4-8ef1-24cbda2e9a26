{"tool_name": "GET_NODE_METRICS", "schema": "Thought: The user is asking for the schema of the `GET_NODE_METRICS` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of the `GET_NODE_METRICS` function, including their names, types, and whether they are required. I will present this information in a JSON format.\nAction:\n```json\n{\n  \"tool_name\": \"GET_NODE_METRICS\",\n  \"description\": \"Get Kubernetes node metrics\",\n  \"parameters\": [\n    {\n      \"name\": \"cluster\",\n      \"type\": \"str\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"fieldSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"nodeName\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"sortBy\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n```\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_NODE_METRICS\",\n  \"description\": \"Get Kubernetes node metrics\",\n  \"parameters\": [\n    {\n      \"name\": \"cluster\",\n      \"type\": \"str\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"fieldSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"labelSelector\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"nodeName\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    },\n    {\n      \"name\": \"sortBy\",\n      \"type\": \"str\",\n      \"required\": false,\n      \"description\": \"\"\n    }\n  ]\n}\n```"}