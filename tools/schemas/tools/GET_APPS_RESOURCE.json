{"tool_name": "GET_APPS_RESOURCE", "schema": "Thought: The user is asking for the schema of the `GET_APPS_RESOURCE` tool. This information is available in the tool definitions provided. I need to extract the `args` and their properties (type, whether they are required) for the `GET_APPS_RESOURCE` function and present it in a JSON format.\nAction: default_api.GET_APPS_RESOURCE.args\nObservation: {'apiVersion': {'type': 'str', 'description': 'API Version', 'required': True}, 'cluster': {'type': 'str', 'description': 'Cluster name', 'required': True}, 'kind': {'type': 'str', 'description': 'Kind of resource', 'required': True}, 'name': {'type': 'str', 'description': 'Name of the resource', 'required': True}, 'namespace': {'type': 'str', 'description': None, 'required': False}}\nThought: I have retrieved the schema information for the `GET_APPS_RESOURCE` tool. Now I need to format it as a JSON object, including the input parameters, their types, and whether they are required, as requested by the user.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_APPS_RESOURCE\",\n  \"description\": \"Get a specific apps resource (namespace-scoped)\",\n  \"parameters\": {\n    \"apiVersion\": {\n      \"type\": \"str\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    \"cluster\": {\n      \"type\": \"str\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    \"kind\": {\n      \"type\": \"str\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    \"name\": {\n      \"type\": \"str\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    \"namespace\": {\n      \"type\": \"str\",\n      \"description\": null,\n      \"required\": false\n    }\n  }\n}\n```"}