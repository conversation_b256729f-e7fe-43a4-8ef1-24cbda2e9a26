{"tool_name": "GET_POLICY_RESOURCE", "schema": "Thought: The user is asking for the schema of the `GET_POLICY_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of this tool, including their names, types, and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\": \"获取工具 GET_POLICY_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: 我无法直接提供工具的schema信息。但是，我可以告诉你 `GET_POLICY_RESOURCE` 工具的参数：\n\n*   `apiVersion` (string, 必需): API 版本\n*   `cluster` (string, 必需): 集群名称\n*   `kind` (string, 必需): 资源类型\n*   `name` (string, 必需): 资源名称\n*   `namespace` (string, 可选): 命名空间\n\n这些参数的含义和使用方式与 `kubectl describe` 命令类似，用于获取特定策略资源的详细信息。\nThought: The user asked for the schema of the `GET_POLICY_RESOURCE` tool, including input parameters, types, and whether they are required. The previous `KUBERNETES_QUERY_PROMPT` action provided a textual description of the parameters. I need to format this information as a JSON object, as requested by the user. I will construct the JSON manually based on the provided description.\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_POLICY_RESOURCE\",\n  \"description\": \"Get a specific policy resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"API Version\"\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Cluster name\"\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Kind of resource\"\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"required\": true,\n      \"description\": \"Name of the resource\"\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"required\": false,\n      \"description\": \"Namespace\"\n    }\n  ]\n}\n"}