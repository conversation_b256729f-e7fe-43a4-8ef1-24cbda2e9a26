{"tool_name": "DESCRIBE_BATCH_RESOURCE", "description": "Describe a specific batch resource with detailed information (namespace-scoped)", "parameters": [{"name": "apiVersion", "type": "string", "description": "API Version", "required": true}, {"name": "cluster", "type": "string", "description": "Cluster name", "required": true}, {"name": "kind", "type": "string", "description": "Kind of resource", "required": true}, {"name": "name", "type": "string", "description": "Name of the resource", "required": true}, {"name": "namespace", "type": "string", "description": "Namespace of the resource", "required": false}]}