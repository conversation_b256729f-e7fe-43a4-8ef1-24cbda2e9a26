{"tool_name": "GET_STORAGE_RESOURCE", "schema": "Thought: The user is asking for the schema of the `GET_STORAGE_RESOURCE` tool. This information is available in the OpenAPI specification of the tool. I need to describe the parameters of this tool, including their types and whether they are required.\nAction: KUBERNETES_QUERY_PROMPT\nAction Input: {\"task\":\"获取工具 GET_STORAGE_RESOURCE 的完整 schema 信息，包括输入参数、类型、是否必需等详细信息。\"}\nObservation: 好的，以下是 `GET_STORAGE_RESOURCE` 工具的完整 schema 信息，以 JSON 格式呈现：\n\n```json\n{\n  \"tool_name\": \"GET_STORAGE_RESOURCE\",\n  \"description\": \"Get a specific storage.k8s.io resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace of the resource\",\n      \"required\": false\n    }\n  ]\n}\n```\nFinal Answer: ```json\n{\n  \"tool_name\": \"GET_STORAGE_RESOURCE\",\n  \"description\": \"Get a specific storage.k8s.io resource (namespace-scoped)\",\n  \"parameters\": [\n    {\n      \"name\": \"apiVersion\",\n      \"type\": \"string\",\n      \"description\": \"API Version\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cluster\",\n      \"type\": \"string\",\n      \"description\": \"Cluster name\",\n      \"required\": true\n    },\n    {\n      \"name\": \"kind\",\n      \"type\": \"string\",\n      \"description\": \"Kind of resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"name\",\n      \"type\": \"string\",\n      \"description\": \"Name of the resource\",\n      \"required\": true\n    },\n    {\n      \"name\": \"namespace\",\n      \"type\": \"string\",\n      \"description\": \"Namespace of the resource\",\n      \"required\": false\n    }\n  ]\n}\n"}